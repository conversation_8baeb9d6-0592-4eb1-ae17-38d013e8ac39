import ParallaxScrollView from '@/components/parallax-scroll-view';
import {ThemedText} from '@/components/themed-text';
import {ThemedView} from '@/components/themed-view';
import {IconSymbol} from '@/components/ui/icon-symbol';
import {Fonts} from '@/constants/theme';
import {useEffect, useState} from "react";
import {useDebounce} from "@uidotdev/usehooks";
import {FlatList, StyleSheet, TextInput} from 'react-native';

const fetchExercises = async (page: number) => {
    const LIMIT = 25
    return fetch(`https://exercisesdb-sand.vercel.app/api/v1/exercises?limit=${LIMIT}&offset=${page * LIMIT}`)
        .then((response) => response.json())
}

export default function ExercisesScreen() {
    const [searchValue, setSearchValue] = useState("");
    const search = useDebounce(searchValue, 500)

    const [exercises, setExercises] = useState<any[]>([])

    useEffect(() => {
        fetchExercises(0)
            .then((json) => setExercises(json.data))
            .catch((error) => console.error(error))
    }, []);

    return (
        <ParallaxScrollView
            headerBackgroundColor={{light: '#D0D0D0', dark: '#353636'}}
            headerImage={
                <IconSymbol
                    size={310}
                    color="#808080"
                    name="chevron.left.forwardslash.chevron.right"
                    style={styles.headerImage}
                />
            }>
            <ThemedView style={styles.titleContainer}>
                <ThemedText
                    type="title"
                    style={{
                        fontFamily: Fonts.rounded,
                    }}>
                    Exercises
                </ThemedText>
            </ThemedView>
            <TextInput placeholder="Search" style={styles.input} onChangeText={(e) => setSearchValue(e)}/>
            <ThemedText>{search}</ThemedText>

            <FlatList
                data={exercises}
                renderItem={({item}) => <ThemedText>{item.name}</ThemedText>}
                keyExtractor={item => item.exerciseId}

                onEndReached={() => {
                }}></FlatList>
        </ParallaxScrollView>
    );
}

const styles = StyleSheet.create({
    input: {
        color: "#FFF",
        borderColor: "#555",
        borderWidth: 1,
        borderStyle: "solid",
        padding: 8,
        borderRadius: 8
    },
    headerImage: {
        color: '#808080',
        bottom: -90,
        left: -35,
        position: 'absolute',
    },
    titleContainer: {
        flexDirection: 'row',
        gap: 8,
    },
});
